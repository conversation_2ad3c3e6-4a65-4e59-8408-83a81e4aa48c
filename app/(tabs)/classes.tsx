import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassesHeader from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import ClassesTabs from "@/components/screens/classes/classes-tabs";
import { ClassCardSkeleton } from "@/components/screens/classes/class-card/class-skeleton";

import { SearchInput } from "@/components/screens/classes/classes-header/search";
import { FlatList } from "react-native";
import { useClassesQuery } from "@/data/screens/classes/mutations/queries/useClassesQuery";
import { formatDate } from "@/data/common/common.utils";
import { ClassCard } from "@/components/screens/classes/class-card/class-card";
import { ClassesEmptyState } from "@/components/screens/classes/empty-state";

const skeletonData = Array.from({ length: 5 }, (_, index) => ({ id: index }));

export const Classes = () => {
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const { data, isLoading } = useClassesQuery({
    date: formatDate(selectedDate),
  });

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabSelect={setSelectedTab} />
        <VStack space="md" className="pb-6">
          <HorizontalDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
          <SearchInput />

          <VStack space="sm" className="px-4 flex-1">
            {isLoading ? (
              <FlatList
                data={skeletonData}
                renderItem={() => <ClassCardSkeleton />}
                keyExtractor={(item) => String(item.id)}
                showsVerticalScrollIndicator={false}
              />
            ) : data && data.length > 0 ? (
              <FlatList
                data={data}
                renderItem={({ item }) => (
                  <ClassCard
                    key={item.id}
                    {...item}
                    selected_date={formatDate(selectedDate)}
                  />
                )}
                keyExtractor={(item) => String(item.id)}
                showsVerticalScrollIndicator={false}
              />
            ) : (
              <ClassesEmptyState />
            )}
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
