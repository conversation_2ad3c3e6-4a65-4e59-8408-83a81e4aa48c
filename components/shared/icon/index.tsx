import React from "react";
import { createIcon } from "@/components/ui/icon";
import { Path } from "react-native-svg";

const ArrowUpIcon = createIcon({
  viewBox: "0 0 12 12",
  path: (
    <>
      <Path d="M6.00007 3.66667L9.3334 7L2.66674 7L6.00007 3.66667Z" />
    </>
  ),
});
export { ArrowUpIcon };

const ArrowDownIcon = createIcon({
  viewBox: "0 0 12 12",
  path: (
    <>
      <Path d="M5.66671 7.66671L2.33337 4.33337H9.00004L5.66671 7.66671Z" />
    </>
  ),
});
export { ArrowDownIcon };

const WeatherIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M15.565 6.14V4.375M12.44 7.435L11.19 6.185M19.985 10.56H21.75M18.69 7.435L19.94 6.185M13.564 8.195C14.127 7.71821 14.8416 7.45783 15.5794 7.46067C16.3171 7.46352 17.0297 7.72938 17.589 8.2105C18.1485 8.69164 18.5179 9.35655 18.631 10.0857C18.7441 10.8149 18.5934 11.5605 18.206 12.1885M10.215 8.665C12.445 8.545 14.765 9.83 15.535 12.77C16.3938 12.8402 17.195 13.2301 17.7801 13.8627C18.3652 14.4952 18.6916 15.3244 18.6947 16.186C18.6979 17.0477 18.3775 17.8792 17.7971 18.516C17.2166 19.1528 16.4183 19.5485 15.56 19.625H5.94499C1.33999 19 0.709994 12.295 5.94499 11.395C6.35466 10.6053 6.96489 9.93737 7.7144 9.45817C8.46391 8.97898 9.32631 8.70543 10.215 8.665Z" />
    </>
  ),
});
export { WeatherIcon };

const LocationIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M12 21.0003C12.201 21.0003 12.3968 20.9372 12.56 20.82C17.389 17.376 22.514 10.297 17.333 5.181C15.919 3.785 14 3 12 3C10.0049 2.99912 8.08937 3.78285 6.66702 5.182C1.48602 10.298 6.61102 17.378 11.44 20.82C11.6032 20.9372 11.7991 21.0003 12 21.0003ZM14.1213 12.1213C13.5587 12.6839 12.7956 13 12 13C11.2044 13 10.4413 12.6839 9.87868 12.1213C9.31607 11.5587 9 10.7956 9 10C9 9.20435 9.31607 8.44129 9.87868 7.87868C10.4413 7.31607 11.2044 7 12 7C12.7956 7 13.5587 7.31607 14.1213 7.87868C14.6839 8.44129 15 9.20435 15 10C15 10.7956 14.6839 11.5587 14.1213 12.1213Z" />
      <Path
        d="M12.56 20.82L12.2697 20.4129L12.2684 20.4139L12.56 20.82ZM17.333 5.181L17.6843 4.82522L17.6843 4.82519L17.333 5.181ZM12 3L11.9998 3.5H12V3ZM6.66702 5.182L6.31639 4.82555L6.31571 4.82622L6.66702 5.182ZM11.44 20.82L11.7317 20.4139L11.7302 20.4128L11.44 20.82ZM12.2684 20.4139C12.1902 20.47 12.0963 20.5003 12 20.5003V21.5003C12.3056 21.5003 12.6035 21.4044 12.8517 21.2261L12.2684 20.4139ZM16.9817 5.53678C18.2013 6.74111 18.7973 8.0402 18.9556 9.35824C19.115 10.6846 18.8352 12.0661 18.2419 13.4278C17.051 16.1611 14.6344 18.7265 12.2697 20.4129L12.8503 21.2271C15.3147 19.4695 17.875 16.7734 19.1587 13.8272C19.8026 12.3492 20.1344 10.7864 19.9485 9.23894C19.7615 7.68317 19.0552 6.17889 17.6843 4.82522L16.9817 5.53678ZM12 3.5C13.8694 3.5 15.662 4.23384 16.9817 5.53681L17.6843 4.82519C16.1761 3.33617 14.1306 2.5 12 2.5V3.5ZM7.01766 5.53845C8.34643 4.23135 10.1359 3.49918 11.9998 3.5L12.0002 2.5C9.87382 2.49907 7.83231 3.33435 6.31639 4.82555L7.01766 5.53845ZM11.7302 20.4128C9.36566 18.7274 6.94903 16.1624 5.75816 13.429C5.16488 12.0673 4.88505 10.6858 5.04442 9.35937C5.2028 8.04126 5.79871 6.7421 7.01834 5.53778L6.31571 4.82622C4.94484 6.1799 4.2385 7.68424 4.05156 9.24007C3.86562 10.7876 4.19742 12.3504 4.84139 13.8285C6.12501 16.7746 8.68539 19.4706 11.1498 21.2272L11.7302 20.4128ZM12 20.5003C11.9037 20.5003 11.8099 20.47 11.7317 20.4139L11.1484 21.2261C11.3966 21.4044 11.6944 21.5003 12 21.5003V20.5003ZM12 13.5C12.9283 13.5 13.8185 13.1313 14.4749 12.4749L13.7678 11.7678C13.2989 12.2366 12.663 12.5 12 12.5V13.5ZM9.52513 12.4749C10.1815 13.1313 11.0717 13.5 12 13.5V12.5C11.337 12.5 10.7011 12.2366 10.2322 11.7678L9.52513 12.4749ZM8.5 10C8.5 10.9283 8.86875 11.8185 9.52513 12.4749L10.2322 11.7678C9.76339 11.2989 9.5 10.663 9.5 10H8.5ZM9.52513 7.52513C8.86875 8.1815 8.5 9.07174 8.5 10H9.5C9.5 9.33696 9.76339 8.70107 10.2322 8.23223L9.52513 7.52513ZM12 6.5C11.0717 6.5 10.1815 6.86875 9.52513 7.52513L10.2322 8.23223C10.7011 7.76339 11.337 7.5 12 7.5V6.5ZM14.4749 7.52513C13.8185 6.86875 12.9283 6.5 12 6.5V7.5C12.663 7.5 13.2989 7.76339 13.7678 8.23223L14.4749 7.52513ZM15.5 10C15.5 9.07174 15.1313 8.1815 14.4749 7.52513L13.7678 8.23223C14.2366 8.70107 14.5 9.33696 14.5 10H15.5ZM14.4749 12.4749C15.1313 11.8185 15.5 10.9283 15.5 10H14.5C14.5 10.663 14.2366 11.2989 13.7678 11.7678L14.4749 12.4749Z"
        stroke="none"
      />
    </>
  ),
});
export { LocationIcon };

const MapsIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M2 3L8.5 6L15.5 3L22 6V21L15.5 18L8.5 21L2 18V3Z" />
      <Path d="M8.5 6V21M15.5 3V18" />
    </>
  ),
});
export { MapsIcon };

const SettingsIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M12.0128 9.32145C11.4605 9.26691 10.9049 9.38439 10.422 9.65782C9.93915 9.93125 9.55257 10.3473 9.31524 10.8489C9.07791 11.3505 9.00143 11.9132 9.09626 12.46C9.1911 13.0068 9.45262 13.5108 9.84501 13.9032C10.2374 14.2956 10.7415 14.5572 11.2883 14.652C11.835 14.7468 12.3978 14.6704 12.8994 14.433C13.401 14.1957 13.817 13.8091 14.0904 13.3262C14.3639 12.8433 14.4814 12.2877 14.4268 11.7355C14.3646 11.1164 14.0903 10.5379 13.6504 10.0979C13.2104 9.65794 12.6319 9.38363 12.0128 9.32145ZM18.4936 12C18.492 12.2926 18.4705 12.5847 18.4293 12.8743L20.3306 14.3657C20.4134 14.4343 20.4692 14.53 20.4881 14.6359C20.507 14.7418 20.4877 14.8509 20.4337 14.9439L18.6349 18.0561C18.5803 18.1482 18.4949 18.2181 18.3938 18.2534C18.2927 18.2887 18.1824 18.2872 18.0823 18.2491L16.194 17.4887C16.0899 17.4473 15.9771 17.4323 15.8657 17.4451C15.7544 17.458 15.648 17.4983 15.556 17.5623C15.2678 17.7608 14.965 17.9371 14.6501 18.0897C14.5511 18.1378 14.4655 18.2096 14.4008 18.2986C14.3361 18.3877 14.2944 18.4913 14.2792 18.6003L13.9962 20.6143C13.9776 20.7207 13.9226 20.8173 13.8407 20.8877C13.7588 20.958 13.655 20.9977 13.547 21H9.94952C9.84335 20.9982 9.74097 20.9602 9.65925 20.8924C9.57752 20.8246 9.52134 20.731 9.49994 20.627L9.21733 18.6158C9.20146 18.5056 9.1586 18.401 9.09256 18.3114C9.02653 18.2217 8.93937 18.1498 8.83882 18.1019C8.52431 17.9502 8.22249 17.7734 7.9363 17.5733C7.84466 17.5095 7.73859 17.4696 7.62765 17.457C7.51672 17.4444 7.40441 17.4597 7.30083 17.5014L5.41293 18.2613C5.31289 18.2994 5.2026 18.301 5.10151 18.2657C5.00042 18.2305 4.91501 18.1607 4.86032 18.0687L3.06158 14.9565C3.0075 14.8636 2.98818 14.7544 3.00704 14.6485C3.02591 14.5426 3.08175 14.4469 3.16462 14.3783L4.77158 13.1166C4.85962 13.0467 4.92881 12.9559 4.97288 12.8525C5.01694 12.7491 5.03449 12.6363 5.02392 12.5244C5.00878 12.3491 4.99952 12.1741 4.99952 11.9987C4.99952 11.8234 5.00835 11.6509 5.02392 11.4793C5.03333 11.3681 5.01493 11.2563 4.97036 11.154C4.9258 11.0516 4.85647 10.962 4.76864 10.8931L3.16251 9.6314C3.08099 9.56247 3.02633 9.46705 3.00811 9.36185C2.98988 9.25666 3.00926 9.14841 3.06284 9.05607L4.86158 5.94393C4.9162 5.85182 5.00159 5.78195 5.10269 5.74664C5.20378 5.71132 5.3141 5.71283 5.4142 5.75089L7.30251 6.51126C7.40665 6.55272 7.51944 6.5677 7.63078 6.55486C7.74213 6.54201 7.84855 6.50175 7.9405 6.43766C8.2287 6.23921 8.53153 6.06291 8.84639 5.91028C8.94538 5.86217 9.031 5.79042 9.0957 5.70138C9.16039 5.61234 9.20216 5.50873 9.21733 5.39972L9.50036 3.38565C9.51895 3.27929 9.57391 3.18268 9.65583 3.11233C9.73776 3.04199 9.84157 3.00229 9.94952 3H13.547C13.6532 3.00184 13.7556 3.03979 13.8373 3.10759C13.919 3.1754 13.9752 3.26902 13.9966 3.37304L14.2792 5.38416C14.2951 5.49437 14.3379 5.59896 14.404 5.68862C14.47 5.77828 14.5572 5.85023 14.6577 5.89808C14.9722 6.04984 15.274 6.22662 15.5602 6.42673C15.6519 6.49049 15.7579 6.53045 15.8689 6.543C15.9798 6.55556 16.0921 6.54031 16.1957 6.49865L18.0836 5.73869C18.1836 5.70059 18.2939 5.69902 18.395 5.73426C18.4961 5.7695 18.5815 5.83928 18.6362 5.93131L20.4349 9.04346C20.489 9.13644 20.5083 9.24559 20.4895 9.35149C20.4706 9.45739 20.4148 9.55315 20.3319 9.62173L18.7249 10.8834C18.6365 10.9531 18.5669 11.0437 18.5225 11.1472C18.478 11.2506 18.4602 11.3635 18.4705 11.4756C18.4844 11.6497 18.4936 11.8246 18.4936 12Z" />
    </>
  ),
});
export { SettingsIcon };

const ActiveWeatherIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M10.215 8.665C12.445 8.545 14.765 9.83 15.535 12.77C16.3938 12.8402 17.195 13.2301 17.7801 13.8627C18.3652 14.4952 18.6916 15.3244 18.6948 16.186C18.6979 17.0477 18.3776 17.8792 17.7971 18.516C17.2166 19.1528 16.4183 19.5485 15.56 19.625H5.945C1.34 19 0.710002 12.295 5.945 11.395C6.35467 10.6053 6.9649 9.93737 7.71441 9.45817C8.46392 8.97898 9.32632 8.70543 10.215 8.665ZM15.565 6.14V4.375V6.14ZM12.44 7.435L11.19 6.185L12.44 7.435ZM19.985 10.56H21.75H19.985ZM18.69 7.435L19.94 6.185L18.69 7.435ZM13.564 8.195C14.127 7.71821 14.8416 7.45783 15.5794 7.46067C16.3171 7.46352 17.0297 7.72938 17.589 8.2105C18.1485 8.69164 18.5179 9.35655 18.631 10.0857C18.7441 10.8149 18.5934 11.5605 18.206 12.1885" />
      <Path d="M15.565 6.14V4.375M12.44 7.435L11.19 6.185M19.985 10.56H21.75M18.69 7.435L19.94 6.185M10.215 8.665C12.445 8.545 14.765 9.83 15.535 12.77C16.3938 12.8402 17.195 13.2301 17.7801 13.8627C18.3652 14.4952 18.6916 15.3244 18.6948 16.186C18.6979 17.0477 18.3776 17.8792 17.7971 18.516C17.2166 19.1528 16.4183 19.5485 15.56 19.625H5.945C1.34 19 0.710002 12.295 5.945 11.395C6.35467 10.6053 6.9649 9.93737 7.71441 9.45817C8.46392 8.97898 9.32632 8.70543 10.215 8.665ZM13.564 8.195C14.127 7.71821 14.8416 7.45783 15.5794 7.46067C16.3171 7.46352 17.0297 7.72938 17.589 8.2105C18.1485 8.69164 18.5179 9.35655 18.631 10.0857C18.7441 10.8149 18.5934 11.5605 18.206 12.1885L13.564 8.195Z" />
    </>
  ),
});
export { ActiveWeatherIcon };

const ActiveLocationIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M11.9998 21.0003C12.2007 21.0003 12.3966 20.9372 12.5598 20.82C17.3888 17.376 22.5138 10.297 17.3328 5.181C15.9188 3.785 13.9998 3 11.9998 3C10.0046 2.99912 8.08912 3.78285 6.66678 5.182C1.48578 10.298 6.61078 17.378 11.4398 20.82C11.603 20.9372 11.7988 21.0003 11.9998 21.0003ZM14.1211 12.1213C13.5585 12.6839 12.7954 13 11.9998 13C11.2041 13 10.441 12.6839 9.87844 12.1213C9.31583 11.5587 8.99976 10.7957 8.99976 10C8.99976 9.20435 9.31583 8.44129 9.87844 7.87868C10.441 7.31607 11.2041 7 11.9998 7C12.7954 7 13.5585 7.31607 14.1211 7.87868C14.6837 8.44129 14.9998 9.20435 14.9998 10C14.9998 10.7957 14.6837 11.5587 14.1211 12.1213Z" />
      <Path d="M12.5598 20.82L12.2695 20.4129L12.2681 20.4139L12.5598 20.82ZM17.3328 5.181L17.6841 4.82522L17.6841 4.82519L17.3328 5.181ZM11.9998 3L11.9996 3.5H11.9998V3ZM6.66678 5.182L6.31614 4.82555L6.31547 4.82622L6.66678 5.182ZM11.4398 20.82L11.7315 20.4139L11.73 20.4128L11.4398 20.82ZM12.2681 20.4139C12.1899 20.47 12.0961 20.5003 11.9998 20.5003V21.5003C12.3054 21.5003 12.6032 21.4044 12.8514 21.2261L12.2681 20.4139ZM16.9815 5.53678C18.2011 6.74111 18.797 8.0402 18.9554 9.35825C19.1148 10.6846 18.8349 12.0661 18.2416 13.4278C17.0508 16.1611 14.6341 18.7265 12.2695 20.4129L12.8501 21.2271C15.3144 19.4695 17.8748 16.7734 19.1584 13.8272C19.8024 12.3492 20.1342 10.7864 19.9482 9.23895C19.7613 7.68317 19.055 6.17889 17.6841 4.82522L16.9815 5.53678ZM11.9998 3.5C13.8692 3.5 15.6617 4.23384 16.9815 5.53681L17.6841 4.82519C16.1758 3.33617 14.1304 2.5 11.9998 2.5V3.5ZM7.01742 5.53845C8.34618 4.23135 10.1357 3.49918 11.9996 3.5L12 2.5C9.87358 2.49907 7.83206 3.33435 6.31614 4.82555L7.01742 5.53845ZM11.73 20.4128C9.36542 18.7274 6.94879 16.1624 5.75791 13.429C5.16464 12.0673 4.88481 10.6858 5.04418 9.35937C5.20255 8.04126 5.79847 6.7421 7.0181 5.53778L6.31547 4.82622C4.94459 6.1799 4.23826 7.68425 4.05132 9.24007C3.86538 10.7876 4.19718 12.3504 4.84115 13.8285C6.12477 16.7746 8.68514 19.4706 11.1496 21.2272L11.73 20.4128ZM11.9998 20.5003C11.9035 20.5003 11.8096 20.47 11.7314 20.4139L11.1481 21.2261C11.3963 21.4044 11.6942 21.5003 11.9998 21.5003V20.5003ZM11.9998 13.5C12.928 13.5 13.8183 13.1313 14.4746 12.4749L13.7675 11.7678C13.2987 12.2366 12.6628 12.5 11.9998 12.5V13.5ZM9.52488 12.4749C10.1813 13.1313 11.0715 13.5 11.9998 13.5V12.5C11.3367 12.5 10.7008 12.2366 10.232 11.7678L9.52488 12.4749ZM8.49976 10C8.49976 10.9283 8.86851 11.8185 9.52488 12.4749L10.232 11.7678C9.76315 11.2989 9.49976 10.663 9.49976 10H8.49976ZM9.52488 7.52513C8.86851 8.1815 8.49976 9.07174 8.49976 10H9.49976C9.49976 9.33696 9.76315 8.70108 10.232 8.23223L9.52488 7.52513ZM11.9998 6.5C11.0715 6.5 10.1813 6.86875 9.52488 7.52513L10.232 8.23223C10.7008 7.76339 11.3367 7.5 11.9998 7.5V6.5ZM14.4746 7.52513C13.8183 6.86875 12.928 6.5 11.9998 6.5V7.5C12.6628 7.5 13.2987 7.76339 13.7675 8.23223L14.4746 7.52513ZM15.4998 10C15.4998 9.07174 15.131 8.1815 14.4746 7.52513L13.7675 8.23223C14.2364 8.70108 14.4998 9.33696 14.4998 10H15.4998ZM14.4746 12.4749C15.131 11.8185 15.4998 10.9283 15.4998 10H14.4998C14.4998 10.663 14.2364 11.2989 13.7675 11.7678L14.4746 12.4749Z" />
    </>
  ),
});
export { ActiveLocationIcon };

const ActiveMapsIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M2 3L8.5 6L15.5 3L22 6V21L15.5 18L8.5 21L2 18V3Z" />
      <Path d="M8.5 6V21M15.5 3V18" />
    </>
  ),
});
export { ActiveMapsIcon };

const ActiveSettingsIcon = createIcon({
  viewBox: "0 0 24 24",
  path: (
    <>
      <Path d="M12.0128 9.32145C11.4605 9.26691 10.9049 9.38439 10.422 9.65782C9.93915 9.93125 9.55257 10.3473 9.31524 10.8489C9.07791 11.3505 9.00143 11.9132 9.09626 12.46C9.1911 13.0068 9.45262 13.5108 9.84501 13.9032C10.2374 14.2956 10.7415 14.5572 11.2883 14.652C11.835 14.7468 12.3978 14.6704 12.8994 14.433C13.401 14.1957 13.817 13.8091 14.0904 13.3262C14.3639 12.8433 14.4814 12.2877 14.4268 11.7355C14.3646 11.1164 14.0903 10.5379 13.6504 10.0979C13.2104 9.65794 12.6319 9.38363 12.0128 9.32145ZM18.4936 12C18.492 12.2926 18.4705 12.5847 18.4293 12.8743L20.3306 14.3657C20.4134 14.4343 20.4692 14.53 20.4881 14.6359C20.507 14.7418 20.4877 14.8509 20.4337 14.9439L18.6349 18.0561C18.5803 18.1482 18.4949 18.2181 18.3938 18.2534C18.2927 18.2887 18.1824 18.2872 18.0823 18.2491L16.194 17.4887C16.0899 17.4473 15.9771 17.4323 15.8657 17.4451C15.7544 17.458 15.648 17.4983 15.556 17.5623C15.2678 17.7608 14.965 17.9371 14.6501 18.0897C14.5511 18.1378 14.4655 18.2096 14.4008 18.2986C14.3361 18.3877 14.2944 18.4913 14.2792 18.6003L13.9962 20.6143C13.9776 20.7207 13.9226 20.8173 13.8407 20.8877C13.7588 20.958 13.655 20.9977 13.547 21H9.94952C9.84335 20.9982 9.74097 20.9602 9.65925 20.8924C9.57752 20.8246 9.52134 20.731 9.49994 20.627L9.21733 18.6158C9.20146 18.5056 9.1586 18.401 9.09257 18.3114C9.02653 18.2217 8.93937 18.1498 8.83882 18.1019C8.52431 17.9502 8.22249 17.7734 7.9363 17.5733C7.84466 17.5095 7.73859 17.4696 7.62766 17.457C7.51672 17.4444 7.40441 17.4597 7.30083 17.5014L5.41293 18.2613C5.31289 18.2994 5.2026 18.301 5.10151 18.2657C5.00042 18.2305 4.91501 18.1607 4.86032 18.0687L3.06158 14.9565C3.0075 14.8636 2.98818 14.7544 3.00704 14.6485C3.02591 14.5426 3.08175 14.4469 3.16462 14.3783L4.77158 13.1166C4.85962 13.0467 4.92881 12.9559 4.97288 12.8525C5.01694 12.7491 5.03449 12.6363 5.02392 12.5244C5.00878 12.3491 4.99952 12.1741 4.99952 11.9987C4.99952 11.8234 5.00835 11.6509 5.02392 11.4793C5.03333 11.3681 5.01493 11.2563 4.97036 11.154C4.9258 11.0516 4.85647 10.962 4.76864 10.8931L3.16251 9.6314C3.08099 9.56247 3.02633 9.46705 3.00811 9.36186C2.98988 9.25666 3.00926 9.14841 3.06284 9.05608L4.86158 5.94393C4.9162 5.85182 5.00159 5.78195 5.10269 5.74663C5.20378 5.71132 5.3141 5.71283 5.4142 5.75089L7.30251 6.51126C7.40665 6.55272 7.51944 6.5677 7.63078 6.55486C7.74213 6.54201 7.84855 6.50175 7.9405 6.43766C8.2287 6.23921 8.53153 6.06291 8.84639 5.91028C8.94538 5.86217 9.031 5.79042 9.0957 5.70138C9.16039 5.61234 9.20216 5.50873 9.21733 5.39972L9.50036 3.38565C9.51895 3.27929 9.57391 3.18268 9.65583 3.11233C9.73776 3.04199 9.84157 3.00229 9.94952 3H13.547C13.6532 3.00184 13.7556 3.03979 13.8373 3.10759C13.919 3.1754 13.9752 3.26902 13.9966 3.37304L14.2792 5.38416C14.2951 5.49437 14.3379 5.59896 14.404 5.68862C14.47 5.77828 14.5572 5.85023 14.6577 5.89808C14.9722 6.04984 15.274 6.22662 15.5602 6.42673C15.6519 6.49049 15.7579 6.53045 15.8689 6.543C15.9798 6.55556 16.0921 6.54031 16.1957 6.49865L18.0836 5.73869C18.1836 5.70059 18.2939 5.69902 18.395 5.73426C18.4961 5.7695 18.5815 5.83928 18.6362 5.93131L20.4349 9.04346C20.489 9.13644 20.5083 9.24559 20.4895 9.35149C20.4706 9.45739 20.4148 9.55315 20.3319 9.62173L18.7249 10.8834C18.6365 10.9531 18.5669 11.0437 18.5225 11.1472C18.478 11.2506 18.4602 11.3635 18.4705 11.4756C18.4844 11.6497 18.4936 11.8246 18.4936 12Z" />
    </>
  ),
});
export { ActiveSettingsIcon };

const NoClassesIcon = createIcon({
  viewBox: "0 0 120 120",
  path: (
    <>
      {/* Light blue background circles */}
      <Path
        d="M60 120C93.1371 120 120 93.1371 120 60C120 26.8629 93.1371 0 60 0C26.8629 0 0 26.8629 0 60C0 93.1371 26.8629 120 60 120Z"
        fill="#E0F7FA"
        opacity="0.3"
      />
      <Path
        d="M45 90C62.6731 90 77 75.6731 77 58C77 40.3269 62.6731 26 45 26C27.3269 26 13 40.3269 13 58C13 75.6731 27.3269 90 45 90Z"
        fill="#B2EBF2"
        opacity="0.4"
      />
      <Path
        d="M85 75C96.0457 75 105 66.0457 105 55C105 43.9543 96.0457 35 85 35C73.9543 35 65 43.9543 65 55C65 66.0457 73.9543 75 85 75Z"
        fill="#B2EBF2"
        opacity="0.2"
      />

      {/* Main people icons */}
      {/* Person 1 - larger circle (head) */}
      <Path
        d="M50 65C56.6274 65 62 59.6274 62 53C62 46.3726 56.6274 41 50 41C43.3726 41 38 46.3726 38 53C38 59.6274 43.3726 65 50 65Z"
        fill="#00BCD4"
      />
      {/* Person 1 - body (ellipse) */}
      <Path
        d="M50 85C61.0457 85 70 78.732 70 71C70 63.268 61.0457 67 50 67C38.9543 67 30 63.268 30 71C30 78.732 38.9543 85 50 85Z"
        fill="#00BCD4"
      />

      {/* Person 2 - smaller circle (head) */}
      <Path
        d="M75 60C80.5228 60 85 55.5228 85 50C85 44.4772 80.5228 40 75 40C69.4772 40 65 44.4772 65 50C65 55.5228 69.4772 60 75 60Z"
        fill="#00ACC1"
      />
      {/* Person 2 - body (ellipse) */}
      <Path
        d="M75 75C83.2843 75 90 70.9706 90 65.5C90 60.0294 83.2843 62 75 62C66.7157 62 60 60.0294 60 65.5C60 70.9706 66.7157 75 75 75Z"
        fill="#00ACC1"
      />

      {/* Person 3 - smallest circle (head) - partially behind */}
      <Path
        d="M85 55C89.4183 55 93 51.4183 93 47C93 42.5817 89.4183 39 85 39C80.5817 39 77 42.5817 77 47C77 51.4183 80.5817 55 85 55Z"
        fill="#0097A7"
      />
      {/* Person 3 - body (ellipse) - partially behind */}
      <Path
        d="M85 67C91.6274 67 97 63.6421 97 59.5C97 55.3579 91.6274 57 85 57C78.3726 57 73 55.3579 73 59.5C73 63.6421 78.3726 67 85 67Z"
        fill="#0097A7"
      />
    </>
  ),
});
export { NoClassesIcon };
