import React from "react";
import { But<PERSON>, ButtonText } from "@/components/ui/button"; // Adjust import path
import { Text } from "react-native";

type ReservationStatus = "available" | "cancelled" | "waitlist" | "reserved";

interface StatusButtonProps {
  status: ReservationStatus;
  onReserve?: (e: any) => void;
  onJoinWaitlist?: (e: any) => void;
  onViewReservation?: (e: any) => void;
}

type ButtonVariant = "outline" | "solid" | "link" | undefined;

// Configuration object for button variants (testable, pure function)
const getButtonConfig = (status: ReservationStatus) => {
  const configs = {
    available: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-[#E6F9FC]",
      textClassName: "text-[#00697B] font-dm-sans-medium text-sm",
      text: "Reserve",
      actionType: "reserve" as const,
    },
    cancelled: {
      variant: "outline" as <PERSON><PERSON>Variant,
      className: "bg-error-50 rounded-2xl border border-error-500",
      textClassName: "text-error-500 font-dm-sans-medium text-sm font-bolder",
      text: "Cancel reservation",
      actionType: null,
    },
    waitlist: {
      variant: "outline" as ButtonVariant,
      className: "rounded-2xl bg-success-50 border border-success-300",
      textClassName: "font-dm-sans-medium text-sm text-[#088432] font-bolder",
      text: "Join waitlist",
      actionType: "waitlist" as const,
    },
    reserved: {
      variant: "solid" as ButtonVariant,
      className: "bg-primary-500 border-0 px-4 py-2 rounded-lg",
      textClassName: "text-white font-dm-sans-medium text-sm",
      text: "Reserve",
      actionType: "viewReservation" as const,
    },
  };

  return configs[status] || null;
};

// Helper to get the appropriate handler
const getActionHandler = (
  actionType: string | null,
  handlers: Pick<
    StatusButtonProps,
    "onReserve" | "onJoinWaitlist" | "onViewReservation"
  >
) => {
  const actionMap = {
    reserve: handlers.onReserve,
    waitlist: handlers.onJoinWaitlist,
    viewReservation: handlers.onViewReservation,
  };

  return actionType
    ? actionMap[actionType as keyof typeof actionMap]
    : undefined;
};

// Main component
export const StatusButton: React.FC<StatusButtonProps> = ({
  status = "available",
  onReserve,
  onJoinWaitlist,
  onViewReservation,
}) => {
  const config = getButtonConfig(status);

  if (!config) {
    return null;
  }

  const handler = getActionHandler(config.actionType, {
    onReserve,
    onJoinWaitlist,
    onViewReservation,
  });

  return (
    <Button
      action="secondary"
      size="sm"
      variant={config.variant}
      className={config.className}
      onPress={(e) => {
        e.stopPropagation();
        handler?.(e);
      }}
    >
      <ButtonText className={config.textClassName ?? config.className}>
        {config.text}
      </ButtonText>
    </Button>
  );
};
