import React from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Icon } from "@/components/ui/icon";
import { NoClassesIcon } from "@/components/shared/icon";

interface EmptyStateProps {
  title?: string;
  subtitle?: string;
}

export const ClassesEmptyState: React.FC<EmptyStateProps> = ({
  title = "There are no classes available",
  subtitle = "Click on another date to see more classes",
}) => {
  return (
    <VStack className="flex-1 justify-center items-center px-4 py-8">
      {/* SVG Icon */}
      <Icon as={NoClassesIcon} size={120} className="mb-6" />

      {/* Title */}
      <Text className="text-lg font-dm-sans-medium text-typography-900 text-center mb-2">
        {title}
      </Text>

      {/* Subtitle */}
      <Text className="text-sm font-dm-sans-regular text-typography-600 text-center">
        {subtitle}
      </Text>
    </VStack>
  );
};

export default ClassesEmptyState;
