import { Icon } from "@/components/ui/icon";
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input";
import { Pressable } from "@/components/ui/pressable";

import { VStack } from "@/components/ui/vstack";
import { Search, SlidersHorizontal } from "lucide-react-native";

export const SearchInput = () => {
  return (
    <VStack space="md" className="px-4  pb-2">
      <Input variant="outline" className=" bg-white rounded-xl" size="lg">
        <InputSlot>
          <InputIcon as={Search} className="text-typography-400 ml-3" />
        </InputSlot>
        <InputField
          placeholder="Search"
          className="placeholder:text-typography-400"
        />
        <InputSlot>
          <Pressable className="p-2 bg-background-100 rounded-lg mr-2 border border-background-200">
            <Icon as={SlidersHorizontal} size="sm" />
          </Pressable>
        </InputSlot>
      </Input>
    </VStack>
  );
};
